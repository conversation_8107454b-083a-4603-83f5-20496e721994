# 渡鸦占卜师使用情况检查脚本

## 功能说明

这个脚本用于检查指定用户是否使用过渡鸦占卜师，实现与后端 API `/api/user/check-raven-usage` 相同的检查逻辑。

## 检查逻辑

脚本会查询 `sessions` 表，检查是否存在满足以下条件的记录：
- `user_id` 等于指定的用户ID
- `reader_id = 'raven'` 或 `reader_name = '渡鸦'`

如果找到任何一条记录，就认为该用户已经使用过渡鸦占卜师。

## 使用方法

### 1. 检查指定用户
```bash
node scripts/check_raven_usage.js 389ea39d-f58d-48fa-8593-d5c6b9fef6c6
```

### 2. 使用默认测试用户ID
```bash
node scripts/check_raven_usage.js
```
默认会检查用户ID: `389ea39d-f58d-48fa-8593-d5c6b9fef6c6`

## 输出信息

脚本会显示以下信息：

### 用户基本信息
- 用户名
- 邮箱
- VIP状态
- 剩余占卜次数
- 注册时间
- 实际剩余次数（考虑已使用次数）

### 渡鸦占卜师使用情况
- 是否使用过渡鸦
- 渡鸦使用次数
- 总占卜次数
- 检查时间

### 详细使用记录（如果使用过）
- 会话ID
- 问题内容
- 占卜师信息
- 牌阵信息
- 状态
- 使用时间

## 示例输出

```
开始检查用户 389ea39d-f58d-48fa-8593-d5c6b9fef6c6 的渡鸦占卜师使用情况...
数据库连接成功

=== 渡鸦占卜师使用情况检查结果 ===
用户ID: 389ea39d-f58d-48fa-8593-d5c6b9fef6c6

--- 用户基本信息 ---
用户名: testuser
邮箱: <EMAIL>
VIP状态: none
剩余占卜次数: 5
注册时间: 2024-01-15 10:30:00
实际剩余次数: 3

--- 渡鸦占卜师使用情况 ---
是否使用过渡鸦: 是
渡鸦使用次数: 1
总占卜次数: 2
检查时间: 2024-01-20T08:30:00.000Z

--- 渡鸦使用记录详情 ---

记录 1:
  会话ID: abc123-def456-ghi789
  问题: 我的感情运势如何？
  占卜师ID: raven
  占卜师名称: 渡鸦
  占卜师类型: 高级塔罗师
  牌阵: 三张牌爱情牌阵
  状态: completed
  时间: 2024-01-18 14:25:30

=== 检查完成 ===

💡 建议: 该用户已经使用过渡鸦占卜师，不应再享受首次免费优惠
```

## 依赖要求

- Node.js
- 项目的数据库连接配置
- 正确的环境变量设置（.env文件）

## 注意事项

1. 确保在项目根目录运行脚本
2. 确保数据库连接配置正确
3. 脚本使用项目现有的数据库连接服务
4. 出错时会显示详细的错误信息
