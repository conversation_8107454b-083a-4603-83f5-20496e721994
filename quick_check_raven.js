#!/usr/bin/env node

/**
 * 快速检查用户是否使用过渡鸦占卜师
 * 这是一个独立的脚本，不依赖项目的其他文件
 * 
 * 使用方法: 
 * 1. 修改下面的数据库配置
 * 2. 运行: node quick_check_raven.js
 */

const mysql = require('mysql2/promise');

// 数据库配置 - 请根据实际情况修改
const DB_CONFIG = {
  host: 'localhost',        // 数据库主机
  port: 3306,              // 数据库端口
  user: 'root',            // 数据库用户名
  password: 'your_password', // 数据库密码
  database: 'tarot',       // 数据库名
  charset: 'utf8mb4'
};

// 要检查的用户ID
const TARGET_USER_ID = '389ea39d-f58d-48fa-8593-d5c6b9fef6c6';

/**
 * 检查用户是否使用过渡鸦占卜师
 */
async function checkRavenUsage() {
  let connection;
  
  try {
    console.log('正在连接数据库...');
    connection = await mysql.createConnection(DB_CONFIG);
    console.log('✅ 数据库连接成功');
    
    console.log(`\n🔍 检查用户: ${TARGET_USER_ID}`);
    
    // 1. 检查用户是否存在
    const [userRows] = await connection.query(
      'SELECT username, email, vip_status, remaining_reads FROM users WHERE id = ?',
      [TARGET_USER_ID]
    );
    
    if (userRows.length === 0) {
      console.log('❌ 用户不存在');
      return;
    }
    
    const user = userRows[0];
    console.log(`👤 用户信息: ${user.username} (${user.email})`);
    console.log(`💎 VIP状态: ${user.vip_status}`);
    console.log(`🎯 剩余次数: ${user.remaining_reads}`);
    
    // 2. 检查是否使用过渡鸦 - 使用与后端相同的逻辑
    const [ravenRows] = await connection.query(`
      SELECT COUNT(*) as count
      FROM sessions 
      WHERE user_id = ? 
      AND (reader_id = 'raven' OR reader_name = '渡鸦')
    `, [TARGET_USER_ID]);
    
    const ravenCount = ravenRows[0].count;
    const hasUsedRaven = ravenCount > 0;
    
    console.log(`\n🦅 渡鸦使用情况:`);
    console.log(`   是否使用过: ${hasUsedRaven ? '✅ 是' : '❌ 否'}`);
    console.log(`   使用次数: ${ravenCount}`);
    
    // 3. 获取总占卜次数（排除每日运势）
    const [totalRows] = await connection.query(`
      SELECT COUNT(*) as count
      FROM sessions 
      WHERE user_id = ? 
      AND spread_id != "daily-fortune"
    `, [TARGET_USER_ID]);
    
    const totalCount = totalRows[0].count;
    console.log(`📊 总占卜次数: ${totalCount}`);
    
    // 4. 计算实际剩余次数
    const actualRemaining = user.vip_status !== 'none' 
      ? '无限制(VIP)' 
      : Math.max(0, user.remaining_reads - totalCount);
    console.log(`⚡ 实际剩余次数: ${actualRemaining}`);
    
    // 5. 如果使用过渡鸦，显示详细记录
    if (hasUsedRaven) {
      console.log(`\n📝 渡鸦使用记录:`);
      const [detailRows] = await connection.query(`
        SELECT 
          id,
          question,
          reader_id,
          reader_name,
          spread_name,
          status,
          timestamp
        FROM sessions 
        WHERE user_id = ? 
        AND (reader_id = 'raven' OR reader_name = '渡鸦')
        ORDER BY timestamp DESC
      `, [TARGET_USER_ID]);
      
      detailRows.forEach((session, index) => {
        console.log(`   ${index + 1}. ${new Date(session.timestamp).toLocaleString('zh-CN')}`);
        console.log(`      问题: ${session.question.substring(0, 40)}${session.question.length > 40 ? '...' : ''}`);
        console.log(`      状态: ${session.status}`);
        console.log(`      牌阵: ${session.spread_name || '未知'}`);
      });
    }
    
    // 6. 给出建议
    console.log(`\n💡 建议:`);
    if (hasUsedRaven) {
      console.log('   该用户已经使用过渡鸦占卜师，不应再享受首次免费优惠');
    } else {
      console.log('   该用户未使用过渡鸦占卜师，可以享受首次免费体验');
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请检查数据库连接配置和数据库服务是否启动');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 提示: 请检查数据库用户名和密码是否正确');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 提示: 请检查数据库名称是否正确');
    }
    
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  console.log('🚀 开始检查渡鸦占卜师使用情况...');
  console.log('=' .repeat(50));
  
  await checkRavenUsage();
  
  console.log('=' .repeat(50));
  console.log('✨ 检查完成');
}

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}
