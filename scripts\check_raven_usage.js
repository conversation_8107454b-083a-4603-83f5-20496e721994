#!/usr/bin/env node

/**
 * 检查用户是否使用过渡鸦占卜师的脚本
 * 使用方法: node scripts/check_raven_usage.js [用户ID]
 * 如果不提供用户ID，将使用默认的测试用户ID
 */

import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// 引入项目的数据库服务
import { getConnection } from '../server/services/database.js';

/**
 * 检查指定用户是否使用过渡鸦占卜师
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 检查结果
 */
async function checkRavenUsage(userId) {
  let pool;
  
  try {
    // 使用项目的数据库连接
    pool = await getConnection();
    console.log('数据库连接成功');
    
    // 执行查询 - 使用与后端相同的逻辑
    const [sessions] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions 
      WHERE user_id = ? 
      AND (reader_id = 'raven' OR reader_name = '渡鸦')
    `, [userId]);
    
    const sessionCount = sessions[0].count;
    const hasUsedRaven = sessionCount > 0;
    
    // 获取用户基本信息
    const [userInfo] = await pool.query(`
      SELECT 
        id,
        username,
        email,
        vip_status,
        remaining_reads,
        created_at
      FROM users 
      WHERE id = ?
    `, [userId]);
    
    // 获取用户总占卜次数（排除每日运势）
    const [totalSessions] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions 
      WHERE user_id = ? 
      AND spread_id != "daily-fortune"
    `, [userId]);
    
    // 如果使用过，获取详细的使用记录
    let usageDetails = [];
    if (hasUsedRaven) {
      const [details] = await pool.query(`
        SELECT 
          id,
          question,
          reader_id,
          reader_name,
          reader_type,
          spread_id,
          spread_name,
          status,
          timestamp
        FROM sessions 
        WHERE user_id = ? 
        AND (reader_id = 'raven' OR reader_name = '渡鸦')
        ORDER BY timestamp DESC
      `, [userId]);
      
      usageDetails = details;
    }
    
    return {
      userId,
      hasUsedRaven,
      sessionCount,
      usageDetails,
      userInfo: userInfo[0] || null,
      totalSessionCount: totalSessions[0].count,
      checkTime: new Date().toISOString()
    };
    
  } catch (error) {
    console.error('检查渡鸦使用情况时出错:', error);
    throw error;
  }
}

/**
 * 格式化输出结果
 * @param {Object} result - 检查结果
 */
function formatResult(result) {
  console.log('\n=== 渡鸦占卜师使用情况检查结果 ===');
  console.log(`用户ID: ${result.userId}`);
  
  // 显示用户基本信息
  if (result.userInfo) {
    console.log('\n--- 用户基本信息 ---');
    console.log(`用户名: ${result.userInfo.username}`);
    console.log(`邮箱: ${result.userInfo.email}`);
    console.log(`VIP状态: ${result.userInfo.vip_status}`);
    console.log(`剩余占卜次数: ${result.userInfo.remaining_reads}`);
    console.log(`注册时间: ${new Date(result.userInfo.created_at).toLocaleString('zh-CN')}`);
    
    // 计算实际剩余次数
    const actualRemainingReads = result.userInfo.vip_status !== 'none' 
      ? '无限制(VIP)' 
      : Math.max(0, result.userInfo.remaining_reads - result.totalSessionCount);
    console.log(`实际剩余次数: ${actualRemainingReads}`);
  } else {
    console.log('⚠️  用户不存在');
    return;
  }
  
  console.log('\n--- 渡鸦占卜师使用情况 ---');
  console.log(`是否使用过渡鸦: ${result.hasUsedRaven ? '是' : '否'}`);
  console.log(`渡鸦使用次数: ${result.sessionCount}`);
  console.log(`总占卜次数: ${result.totalSessionCount}`);
  console.log(`检查时间: ${result.checkTime}`);
  
  if (result.hasUsedRaven && result.usageDetails.length > 0) {
    console.log('\n--- 渡鸦使用记录详情 ---');
    result.usageDetails.forEach((session, index) => {
      console.log(`\n记录 ${index + 1}:`);
      console.log(`  会话ID: ${session.id}`);
      console.log(`  问题: ${session.question.substring(0, 50)}${session.question.length > 50 ? '...' : ''}`);
      console.log(`  占卜师ID: ${session.reader_id}`);
      console.log(`  占卜师名称: ${session.reader_name}`);
      console.log(`  占卜师类型: ${session.reader_type}`);
      console.log(`  牌阵: ${session.spread_name || '未知'}`);
      console.log(`  状态: ${session.status}`);
      console.log(`  时间: ${new Date(session.timestamp).toLocaleString('zh-CN')}`);
    });
  }
  
  console.log('\n=== 检查完成 ===\n');
}

// 主函数
async function main() {
  // 从命令行参数获取用户ID，如果没有提供则使用默认值
  const targetUserId = process.argv[2] || '389ea39d-f58d-48fa-8593-d5c6b9fef6c6';
  
  try {
    console.log(`开始检查用户 ${targetUserId} 的渡鸦占卜师使用情况...`);
    
    const result = await checkRavenUsage(targetUserId);
    formatResult(result);
    
    // 根据结果给出建议
    if (result.userInfo) {
      if (result.hasUsedRaven) {
        console.log('💡 建议: 该用户已经使用过渡鸦占卜师，不应再享受首次免费优惠');
      } else {
        console.log('💡 建议: 该用户未使用过渡鸦占卜师，可以享受首次免费体验');
      }
    }
    
  } catch (error) {
    console.error('脚本执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  checkRavenUsage,
  formatResult
};
