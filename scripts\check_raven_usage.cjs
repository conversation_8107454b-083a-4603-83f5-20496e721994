#!/usr/bin/env node

/**
 * 检查用户是否使用过渡鸦占卜师的脚本 (CommonJS版本)
 * 使用方法: node scripts/check_raven_usage.cjs [用户ID]
 * 如果不提供用户ID，将使用默认的测试用户ID
 */

const path = require('path');

// 修改环境变量文件路径，指向 server/.env
const dotenvPath = path.join(__dirname, '../server/.env');
console.log('尝试加载环境变量文件:', dotenvPath);
require('dotenv').config({ path: dotenvPath });

// 确保设置NODE_ENV环境变量
if (!process.env.NODE_ENV) {
  console.log('NODE_ENV未设置，默认使用development环境');
  process.env.NODE_ENV = 'development';
} else {
  console.log('当前NODE_ENV:', process.env.NODE_ENV);
}

// 引入项目的数据库服务
const { getConnection, initializePool } = require('../server/services/database');

// 打印MySQL配置信息（密码除外）
console.log('MySQL连接配置:');
console.log('- 主机(production):', process.env.MYSQL_HOST_INTERNAL || '未配置');
console.log('- 主机(development):', process.env.MYSQL_HOST_PUBLIC || '未配置');
console.log('- 端口:', process.env.MYSQL_PORT || '未配置');
console.log('- 用户名:', process.env.MYSQL_USERNAME || '未配置');
console.log('- 数据库:', process.env.MYSQL_DATABASE || '未配置');

/**
 * 检查指定用户是否使用过渡鸦占卜师
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 检查结果
 */
async function checkRavenUsage(userId) {
  try {
    // 使用项目的数据库连接池
    const pool = await getConnection();
    console.log('✅ 数据库连接成功');
    
    // 执行查询 - 使用与后端相同的逻辑
    const [sessions] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions
      WHERE user_id = ?
      AND (reader_id = 'raven' OR reader_name = '渡鸦')
    `, [userId]);

    const sessionCount = sessions[0].count;
    const hasUsedRaven = sessionCount > 0;

    // 获取用户基本信息
    const [userInfo] = await pool.query(`
      SELECT
        id,
        username,
        email,
        vip_status,
        remaining_reads,
        created_at
      FROM users
      WHERE id = ?
    `, [userId]);

    // 获取用户总占卜次数（排除每日运势）
    const [totalSessions] = await pool.query(`
      SELECT COUNT(*) as count
      FROM sessions
      WHERE user_id = ?
      AND spread_id != "daily-fortune"
    `, [userId]);

    // 如果使用过，获取详细的使用记录
    let usageDetails = [];
    if (hasUsedRaven) {
      const [details] = await pool.query(`
        SELECT
          id,
          question,
          reader_id,
          reader_name,
          reader_type,
          spread_id,
          spread_name,
          status,
          timestamp
        FROM sessions
        WHERE user_id = ?
        AND (reader_id = 'raven' OR reader_name = '渡鸦')
        ORDER BY timestamp DESC
      `, [userId]);

      usageDetails = details;
    }
    
    return {
      userId,
      hasUsedRaven,
      sessionCount,
      usageDetails,
      userInfo: userInfo[0] || null,
      totalSessionCount: totalSessions[0].count,
      checkTime: new Date().toISOString()
    };
    
  } catch (error) {
    console.error('❌ 检查渡鸦使用情况时出错:', error);
    throw error;
  }
  // 注意：使用连接池时不需要手动关闭连接
}

/**
 * 格式化输出结果
 * @param {Object} result - 检查结果
 */
function formatResult(result) {
  console.log('\n=== 渡鸦占卜师使用情况检查结果 ===');
  console.log(`用户ID: ${result.userId}`);
  
  // 显示用户基本信息
  if (result.userInfo) {
    console.log('\n--- 用户基本信息 ---');
    console.log(`👤 用户名: ${result.userInfo.username}`);
    console.log(`📧 邮箱: ${result.userInfo.email}`);
    console.log(`💎 VIP状态: ${result.userInfo.vip_status}`);
    console.log(`🎯 剩余占卜次数: ${result.userInfo.remaining_reads}`);
    console.log(`📅 注册时间: ${new Date(result.userInfo.created_at).toLocaleString('zh-CN')}`);
    
    // 计算实际剩余次数
    const actualRemainingReads = result.userInfo.vip_status !== 'none' 
      ? '无限制(VIP)' 
      : Math.max(0, result.userInfo.remaining_reads - result.totalSessionCount);
    console.log(`⚡ 实际剩余次数: ${actualRemainingReads}`);
  } else {
    console.log('⚠️  用户不存在');
    return;
  }
  
  console.log('\n--- 渡鸦占卜师使用情况 ---');
  console.log(`🦅 是否使用过渡鸦: ${result.hasUsedRaven ? '✅ 是' : '❌ 否'}`);
  console.log(`📊 渡鸦使用次数: ${result.sessionCount}`);
  console.log(`📈 总占卜次数: ${result.totalSessionCount}`);
  console.log(`🕐 检查时间: ${result.checkTime}`);
  
  if (result.hasUsedRaven && result.usageDetails.length > 0) {
    console.log('\n--- 渡鸦使用记录详情 ---');
    result.usageDetails.forEach((session, index) => {
      console.log(`\n📝 记录 ${index + 1}:`);
      console.log(`   会话ID: ${session.id}`);
      console.log(`   问题: ${session.question.substring(0, 50)}${session.question.length > 50 ? '...' : ''}`);
      console.log(`   占卜师ID: ${session.reader_id}`);
      console.log(`   占卜师名称: ${session.reader_name}`);
      console.log(`   占卜师类型: ${session.reader_type}`);
      console.log(`   牌阵: ${session.spread_name || '未知'}`);
      console.log(`   状态: ${session.status}`);
      console.log(`   时间: ${new Date(session.timestamp).toLocaleString('zh-CN')}`);
    });
  }
  
  console.log('\n=== 检查完成 ===\n');
}

// 主函数
async function main() {
  // 从命令行参数获取用户ID，如果没有提供则使用默认值
  const targetUserId = process.argv[2] || '389ea39d-f58d-48fa-8593-d5c6b9fef6c6';

  try {
    console.log('🚀 开始检查渡鸦占卜师使用情况...');
    console.log('=' .repeat(60));

    // 初始化数据库连接池
    console.log('正在初始化数据库连接...');
    await initializePool()
      .then(() => {
        console.log('成功连接到数据库');
      })
      .catch(err => {
        console.error('无法连接到数据库:', err);
        throw err;
      });

    console.log(`🔍 检查用户: ${targetUserId}`);

    const result = await checkRavenUsage(targetUserId);
    formatResult(result);
    
    // 根据结果给出建议
    if (result.userInfo) {
      console.log('💡 建议:');
      if (result.hasUsedRaven) {
        console.log('   该用户已经使用过渡鸦占卜师，不应再享受首次免费优惠');
      } else {
        console.log('   该用户未使用过渡鸦占卜师，可以享受首次免费体验');
      }
    }
    
    console.log('=' .repeat(60));
    console.log('✨ 检查完成');

    // 脚本执行完毕后退出
    process.exit(0);

  } catch (error) {
    console.error('❌ 脚本执行失败:', error.message);
    
    // 提供一些常见错误的解决建议
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请检查数据库连接配置和数据库服务是否启动');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 提示: 请检查数据库用户名和密码是否正确');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 提示: 请检查数据库名称是否正确');
    }
    
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkRavenUsage,
  formatResult
};
